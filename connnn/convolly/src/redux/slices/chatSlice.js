import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { 
  getMessages, 
  getUserConversations, 
  searchChats, 
  markMessagesAsRead,
  getUnreadCount,
  ChatApiError 
} from '../../pages/messaging/utils/api';

/**
 * Async thunks for chat operations
 */

// Fetch user conversations
export const fetchConversations = createAsyncThunk(
  'chat/fetchConversations',
  async ({ userId, accessToken, options = {} }, { rejectWithValue }) => {
    try {
      const result = await getUserConversations(userId, accessToken, options);
      return result;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

// Fetch conversation messages
export const fetchMessages = createAsyncThunk(
  'chat/fetchMessages',
  async ({ conversationId, accessToken, options = {} }, { rejectWithValue }) => {
    try {
      const result = await getMessages(conversationId, accessToken, options);
      return { conversationId, ...result };
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

// Load more messages (pagination)
export const loadMoreMessages = createAsyncThunk(
  'chat/loadMoreMessages',
  async ({ conversationId, accessToken, cursor }, { rejectWithValue }) => {
    try {
      const result = await getMessages(conversationId, accessToken, { cursor });
      return { conversationId, ...result };
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

// Search chats
export const searchChatData = createAsyncThunk(
  'chat/search',
  async ({ query, accessToken, options = {} }, { rejectWithValue }) => {
    try {
      const result = await searchChats(query, accessToken, options);
      return { query, result };
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

// Mark messages as read
export const markAsRead = createAsyncThunk(
  'chat/markAsRead',
  async ({ conversationId, messageIds, accessToken }, { rejectWithValue }) => {
    try {
      await markMessagesAsRead(conversationId, messageIds, accessToken);
      return { conversationId, messageIds };
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

// Fetch unread count
export const fetchUnreadCount = createAsyncThunk(
  'chat/fetchUnreadCount',
  async ({ userId, accessToken }, { rejectWithValue }) => {
    try {
      const result = await getUnreadCount(userId, accessToken);
      return result;
    } catch (error) {
      return rejectWithValue({
        message: error.message,
        status: error.status,
        details: error.details,
      });
    }
  }
);

/**
 * Initial state
 */
const initialState = {
  // Conversations
  conversations: [],
  conversationsLoading: false,
  conversationsError: null,
  conversationsPagination: {
    hasMore: false,
    nextCursor: null,
  },

  // Messages by conversation ID
  messagesByConversation: {},
  messagesLoading: {},
  messagesError: {},
  messagesPagination: {},

  // Current active conversation
  activeConversationId: null,

  // Search
  searchQuery: '',
  searchResults: [],
  searchLoading: false,
  searchError: null,

  // Typing indicators
  typingUsers: {},

  // Unread counts
  unreadCount: 0,
  unreadByConversation: {},

  // UI state
  isMobileView: false,
  showConversationList: true,
  
  // Real-time updates
  onlineUsers: {},
  
  // File uploads
  uploadingFiles: {},
  
  // Optimistic updates
  pendingMessages: {},
};

/**
 * Chat slice
 */
const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    // UI actions
    setActiveConversation: (state, action) => {
      state.activeConversationId = action.payload;
    },

    setMobileView: (state, action) => {
      state.isMobileView = action.payload;
    },

    toggleConversationList: (state) => {
      state.showConversationList = !state.showConversationList;
    },

    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },

    clearSearchResults: (state) => {
      state.searchResults = [];
      state.searchQuery = '';
      state.searchError = null;
    },

    // Real-time message updates
    addMessage: (state, action) => {
      const { conversationId, message } = action.payload;
      
      if (!state.messagesByConversation[conversationId]) {
        state.messagesByConversation[conversationId] = [];
      }
      
      // Check for duplicates
      const exists = state.messagesByConversation[conversationId].find(
        msg => msg.id === message.id || msg.tempId === message.tempId
      );
      
      if (!exists) {
        state.messagesByConversation[conversationId].push(message);
      }
    },

    updateMessage: (state, action) => {
      const { conversationId, messageId, updates } = action.payload;
      
      if (state.messagesByConversation[conversationId]) {
        const messageIndex = state.messagesByConversation[conversationId].findIndex(
          msg => msg.id === messageId || msg.tempId === messageId
        );
        
        if (messageIndex !== -1) {
          state.messagesByConversation[conversationId][messageIndex] = {
            ...state.messagesByConversation[conversationId][messageIndex],
            ...updates,
          };
        }
      }
    },

    removeMessage: (state, action) => {
      const { conversationId, messageId } = action.payload;
      
      if (state.messagesByConversation[conversationId]) {
        state.messagesByConversation[conversationId] = state.messagesByConversation[conversationId].filter(
          msg => msg.id !== messageId && msg.tempId !== messageId
        );
      }
    },

    // Optimistic updates for sending messages
    addPendingMessage: (state, action) => {
      const { conversationId, message } = action.payload;
      
      if (!state.pendingMessages[conversationId]) {
        state.pendingMessages[conversationId] = [];
      }
      
      state.pendingMessages[conversationId].push(message);
    },

    removePendingMessage: (state, action) => {
      const { conversationId, tempId } = action.payload;
      
      if (state.pendingMessages[conversationId]) {
        state.pendingMessages[conversationId] = state.pendingMessages[conversationId].filter(
          msg => msg.tempId !== tempId
        );
      }
    },

    // Typing indicators
    setUserTyping: (state, action) => {
      const { conversationId, user } = action.payload;
      
      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }
      
      const exists = state.typingUsers[conversationId].find(u => u.id === user.id);
      if (!exists) {
        state.typingUsers[conversationId].push(user);
      }
    },

    removeUserTyping: (state, action) => {
      const { conversationId, userId } = action.payload;
      
      if (state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = state.typingUsers[conversationId].filter(
          user => user.id !== userId
        );
      }
    },

    // Online status
    setUserOnline: (state, action) => {
      const { userId, isOnline } = action.payload;
      state.onlineUsers[userId] = isOnline;
    },

    // Unread counts
    updateUnreadCount: (state, action) => {
      const { conversationId, count } = action.payload;
      state.unreadByConversation[conversationId] = count;
      
      // Update total unread count
      state.unreadCount = Object.values(state.unreadByConversation).reduce(
        (total, count) => total + count, 0
      );
    },

    // File uploads
    setFileUploading: (state, action) => {
      const { conversationId, fileId, uploading } = action.payload;
      
      if (!state.uploadingFiles[conversationId]) {
        state.uploadingFiles[conversationId] = {};
      }
      
      state.uploadingFiles[conversationId][fileId] = uploading;
    },

    // Clear conversation data
    clearConversationMessages: (state, action) => {
      const conversationId = action.payload;
      delete state.messagesByConversation[conversationId];
      delete state.messagesLoading[conversationId];
      delete state.messagesError[conversationId];
      delete state.messagesPagination[conversationId];
    },

    // Reset state
    resetChatState: (state) => {
      return { ...initialState };
    },
  },

  extraReducers: (builder) => {
    // Fetch conversations
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.conversationsLoading = true;
        state.conversationsError = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.conversationsLoading = false;
        state.conversations = action.payload.conversations;
        state.conversationsPagination = action.payload.pagination;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.conversationsLoading = false;
        state.conversationsError = action.payload;
      });

    // Fetch messages
    builder
      .addCase(fetchMessages.pending, (state, action) => {
        const conversationId = action.meta.arg.conversationId;
        state.messagesLoading[conversationId] = true;
        state.messagesError[conversationId] = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        const { conversationId, messages, pagination } = action.payload;
        state.messagesLoading[conversationId] = false;
        state.messagesByConversation[conversationId] = messages;
        state.messagesPagination[conversationId] = pagination;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        const conversationId = action.meta.arg.conversationId;
        state.messagesLoading[conversationId] = false;
        state.messagesError[conversationId] = action.payload;
      });

    // Load more messages
    builder
      .addCase(loadMoreMessages.fulfilled, (state, action) => {
        const { conversationId, messages, pagination } = action.payload;
        
        if (state.messagesByConversation[conversationId]) {
          // Prepend older messages
          state.messagesByConversation[conversationId] = [
            ...messages,
            ...state.messagesByConversation[conversationId],
          ];
        } else {
          state.messagesByConversation[conversationId] = messages;
        }
        
        state.messagesPagination[conversationId] = pagination;
      });

    // Search
    builder
      .addCase(searchChatData.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchChatData.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload.result;
      })
      .addCase(searchChatData.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload;
      });

    // Mark as read
    builder
      .addCase(markAsRead.fulfilled, (state, action) => {
        const { conversationId, messageIds } = action.payload;
        
        // Update messages as read
        if (state.messagesByConversation[conversationId]) {
          state.messagesByConversation[conversationId] = state.messagesByConversation[conversationId].map(
            msg => messageIds.includes(msg.id) ? { ...msg, readAt: new Date().toISOString() } : msg
          );
        }
        
        // Update unread count
        state.unreadByConversation[conversationId] = 0;
        state.unreadCount = Object.values(state.unreadByConversation).reduce(
          (total, count) => total + count, 0
        );
      });

    // Fetch unread count
    builder
      .addCase(fetchUnreadCount.fulfilled, (state, action) => {
        state.unreadCount = action.payload.total || 0;
        state.unreadByConversation = action.payload.byConversation || {};
      });
  },
});

export const {
  setActiveConversation,
  setMobileView,
  toggleConversationList,
  setSearchQuery,
  clearSearchResults,
  addMessage,
  updateMessage,
  removeMessage,
  addPendingMessage,
  removePendingMessage,
  setUserTyping,
  removeUserTyping,
  setUserOnline,
  updateUnreadCount,
  setFileUploading,
  clearConversationMessages,
  resetChatState,
} = chatSlice.actions;

export default chatSlice.reducer;
