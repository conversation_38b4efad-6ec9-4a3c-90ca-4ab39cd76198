// import React, { useState } from "react";
// import PhoneInput from "react-phone-number-input";
// import flags from "country-flag-icons/react/3x2";
// import "react-phone-number-input/style.css";

// const PhoneInputWithCountry = () => {
//   const [value, setValue] = useState();

//   return (
//     <PhoneInput
//       international
//       defaultCountry="US"
//       value={value}
//       onChange={setValue}
//       flags={flags}
//       className="text-sm sm:text-base border border-[#E8E8E8] bg-white p-3 px-4 rounded-lg w-full focus:outline-none outline-none mb-7"
//     />
//   );
// };

// export default PhoneInputWithCountry;


import React from "react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { Controller } from "react-hook-form";
import flags from "country-flag-icons/react/3x2";


const PhoneInputWithCountry = ({ control, name, error, isRequired, rules = {} }) => {
  const validationRules = {
    required: isRequired ? "Phone number is required" : false,
    ...rules
  };

  return (
    <div className="mb-7">
      <Controller
        name={name}
        control={control}
        rules={validationRules}
        render={({ field }) => (
          <PhoneInput
            international
            defaultCountry="US"
            value={field.value}
            onChange={field.onChange}
            onBlur={field.onBlur}
            flags={flags}
            className={`text-sm sm:text-base border ${
              error ? "border-red-500" : "border-[#E8E8E8]"
            } bg-white p-3 px-4 rounded-lg w-full focus:outline-none`}
          />
        )}
      />
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};

export default PhoneInputWithCountry;
