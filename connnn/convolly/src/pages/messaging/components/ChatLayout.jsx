import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useChat } from '../../../hooks/useChat';
import ConversationList from './ConversationList';
import ChatRoom from './ChatRoom';
import { getUser } from '../utils/api';
import { FiArrowLeft, FiMenu, FiX } from 'react-icons/fi';

/**
 * Main chat layout component that manages the conversation list and chat room
 */
const ChatLayout = () => {
  const navigate = useNavigate();
  const { userId } = useParams();
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get('chat_cid');
  
  // Redux state
  const currentUser = useSelector(state => state.app?.userInfo?.user);
  
  // Chat state
  const {
    activeConversationId,
    isMobileView,
    showConversationList,
    setActiveConversationId,
    setMobileView,
    toggleConversationList,
  } = useChat();

  // Local state
  const [otherUser, setOtherUser] = useState(null);
  const [loadingUser, setLoadingUser] = useState(false);
  const [userError, setUserError] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Responsive handling
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setMobileView(mobile);
      
      // Auto-close sidebar on mobile when screen gets larger
      if (!mobile && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    handleResize(); // Initial check
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setMobileView, sidebarOpen]);

  // Load other user data when userId changes
  useEffect(() => {
    const loadOtherUser = async () => {
      if (!userId || userId === currentUser?.id) {
        setOtherUser(null);
        return;
      }

      setLoadingUser(true);
      setUserError(null);

      try {
        const userData = await getUser({ userId });
        setOtherUser(userData);
      } catch (error) {
        console.error('Error loading user:', error);
        setUserError(error.message || 'Failed to load user');
        setOtherUser(null);
      } finally {
        setLoadingUser(false);
      }
    };

    loadOtherUser();
  }, [userId, currentUser?.id]);

  // Update active conversation when conversationId changes
  useEffect(() => {
    if (conversationId && conversationId !== activeConversationId) {
      setActiveConversationId(conversationId);
    }
  }, [conversationId, activeConversationId, setActiveConversationId]);

  // Handle conversation selection
  const handleConversationSelect = (conversation) => {
    if (isMobileView) {
      setSidebarOpen(false);
    }
  };

  // Handle back navigation on mobile
  const handleBackToList = () => {
    if (isMobileView) {
      navigate(`/${currentUser?.role}/messages`);
    }
  };

  // Toggle sidebar on mobile
  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Determine what to show
  const showChatRoom = userId && otherUser;
  const showEmptyState = !userId;

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar Overlay (Mobile) */}
      {isMobileView && sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Conversation List Sidebar */}
      <div className={`
        ${isMobileView 
          ? `fixed left-0 top-0 h-full w-80 z-50 transform transition-transform duration-300 ${
              sidebarOpen ? 'translate-x-0' : '-translate-x-full'
            }`
          : 'w-1/3 min-w-[320px] max-w-[400px]'
        }
        ${!isMobileView && !showConversationList ? 'hidden' : ''}
        bg-white border-r border-gray-200
      `}>
        <ConversationList
          selectedConversationId={activeConversationId}
          onConversationSelect={handleConversationSelect}
          className="h-full"
        />
      </div>

      {/* Main Chat Area */}
      <div className={`
        flex-1 flex flex-col
        ${isMobileView && sidebarOpen ? 'hidden' : ''}
      `}>
        {/* Mobile Header */}
        {isMobileView && (
          <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
            {showChatRoom ? (
              <>
                <button
                  onClick={handleBackToList}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
                >
                  <FiArrowLeft size={20} />
                </button>
                <h1 className="text-lg font-semibold truncate">
                  {otherUser?.firstname || otherUser?.firstName} {otherUser?.lastname || otherUser?.lastName}
                </h1>
                <div className="w-10" /> {/* Spacer */}
              </>
            ) : (
              <>
                <h1 className="text-lg font-semibold">Messages</h1>
                <button
                  onClick={handleToggleSidebar}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md"
                >
                  <FiMenu size={20} />
                </button>
              </>
            )}
          </div>
        )}

        {/* Chat Content */}
        <div className="flex-1 overflow-hidden">
          {showChatRoom ? (
            <ChatRoom
              selected={true}
              userId={userId}
              otherUser={otherUser}
              className="h-full"
              showHeader={!isMobileView}
            />
          ) : showEmptyState ? (
            <EmptyState currentUser={currentUser} />
          ) : loadingUser ? (
            <LoadingState />
          ) : userError ? (
            <ErrorState error={userError} onRetry={() => window.location.reload()} />
          ) : null}
        </div>
      </div>
    </div>
  );
};

/**
 * Empty state when no conversation is selected
 */
const EmptyState = ({ currentUser }) => (
  <div className="h-full flex items-center justify-center bg-gray-50">
    <div className="text-center max-w-md mx-auto p-8">
      <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
        <FiMenu size={32} className="text-gray-400" />
      </div>
      <h2 className="text-2xl font-semibold text-gray-900 mb-4">
        Welcome to Messages
      </h2>
      <p className="text-gray-600 mb-6">
        Select a conversation from the sidebar to start chatting, or start a new conversation.
      </p>
      <button
        onClick={() => {
          // Navigate to user search or conversation creation
          window.location.href = `/${currentUser?.role}/find-users`;
        }}
        className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
      >
        Start New Conversation
      </button>
    </div>
  </div>
);

/**
 * Loading state while fetching user data
 */
const LoadingState = () => (
  <div className="h-full flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-gray-600">Loading conversation...</p>
    </div>
  </div>
);

/**
 * Error state when user loading fails
 */
const ErrorState = ({ error, onRetry }) => (
  <div className="h-full flex items-center justify-center bg-gray-50">
    <div className="text-center max-w-md mx-auto p-8">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <FiX size={24} className="text-red-600" />
      </div>
      <h2 className="text-xl font-semibold text-gray-900 mb-2">
        Failed to Load Conversation
      </h2>
      <p className="text-gray-600 mb-6">
        {error || 'Something went wrong while loading the conversation.'}
      </p>
      <button
        onClick={onRetry}
        className="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>
);

export default ChatLayout;
