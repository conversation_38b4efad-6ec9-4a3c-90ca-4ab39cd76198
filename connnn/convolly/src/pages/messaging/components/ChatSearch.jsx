import React, { useState, useEffect, useRef } from 'react';
import { useChat } from '../../../hooks/useChat';
import { 
  FiSearch, 
  FiX, 
  FiUser, 
  FiMessageCircle, 
  FiFile,
  FiClock
} from 'react-icons/fi';
import dayjs from 'dayjs';
import userVector from '@/assets/svgs/userVector.svg';

/**
 * Enhanced search component for conversations, messages, and files
 */
const ChatSearch = ({ 
  onResultSelect, 
  onClose,
  className = "",
  placeholder = "Search conversations, messages, and files..."
}) => {
  const [query, setQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isOpen, setIsOpen] = useState(false);
  const searchInputRef = useRef(null);
  const resultsRef = useRef(null);

  const {
    searchResults,
    searchLoading,
    searchError,
    searchChats,
    clearSearchResults,
  } = useChat();

  // Search tabs
  const tabs = [
    { id: 'all', label: 'All', icon: FiSearch },
    { id: 'conversations', label: 'Conversations', icon: FiMessageCircle },
    { id: 'messages', label: 'Messages', icon: FiMessageCircle },
    { id: 'files', label: 'Files', icon: FiFile },
    { id: 'users', label: 'People', icon: FiUser },
  ];

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.trim().length >= 2) {
        searchChats(query, { 
          type: activeTab === 'all' ? undefined : activeTab,
          limit: 20 
        });
        setIsOpen(true);
      } else {
        clearSearchResults();
        setIsOpen(false);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, activeTab, searchChats, clearSearchResults]);

  // Handle input change
  const handleInputChange = (e) => {
    setQuery(e.target.value);
  };

  // Handle clear search
  const handleClear = () => {
    setQuery('');
    clearSearchResults();
    setIsOpen(false);
    searchInputRef.current?.focus();
  };

  // Handle result selection
  const handleResultSelect = (result, type) => {
    onResultSelect?.(result, type);
    handleClear();
    onClose?.();
  };

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    if (query.trim().length >= 2) {
      searchChats(query, { 
        type: tabId === 'all' ? undefined : tabId,
        limit: 20 
      });
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        resultsRef.current && 
        !resultsRef.current.contains(event.target) &&
        !searchInputRef.current?.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Render search result item
  const renderResultItem = (item, type) => {
    switch (type) {
      case 'conversations':
        return (
          <div
            key={`conversation-${item.id}`}
            onClick={() => handleResultSelect(item, 'conversation')}
            className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
          >
            <div className="flex items-center gap-3">
              <div className="flex -space-x-1">
                {Object.values(item.participants || {}).slice(0, 2).map((participant, index) => (
                  <img
                    key={participant._id || index}
                    src={participant.profilePicture || participant.image || userVector}
                    alt={participant.firstName || participant.firstname}
                    className="w-8 h-8 rounded-full border-2 border-white"
                  />
                ))}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {Object.values(item.participants || {})
                    .map(p => `${p.firstName || p.firstname} ${p.lastName || p.lastname}`)
                    .join(', ')}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {item.lastMessage?.text || 'No messages yet'}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                <FiMessageCircle size={16} />
              </div>
            </div>
          </div>
        );

      case 'messages':
        return (
          <div
            key={`message-${item.id}`}
            onClick={() => handleResultSelect(item, 'message')}
            className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
          >
            <div className="flex items-start gap-3">
              <img
                src={item.sender?.profilePicture || item.sender?.image || userVector}
                alt={item.sender?.firstName || item.sender?.firstname}
                className="w-8 h-8 rounded-full"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <p className="text-sm font-medium text-gray-900">
                    {item.sender?.firstName || item.sender?.firstname} {item.sender?.lastName || item.sender?.lastname}
                  </p>
                  <span className="text-xs text-gray-500">
                    {dayjs(item.createdAt).fromNow()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {item.text || '[File attachment]'}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                <FiClock size={16} />
              </div>
            </div>
          </div>
        );

      case 'files':
        return (
          <div
            key={`file-${item.id}`}
            onClick={() => handleResultSelect(item, 'file')}
            className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                <FiFile className="text-gray-500" size={16} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {item.name}
                </p>
                <p className="text-xs text-gray-500">
                  {item.size && `${Math.round(item.size / 1024)} KB`} • 
                  {dayjs(item.createdAt).fromNow()}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                <FiFile size={16} />
              </div>
            </div>
          </div>
        );

      case 'users':
        return (
          <div
            key={`user-${item.id}`}
            onClick={() => handleResultSelect(item, 'user')}
            className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
          >
            <div className="flex items-center gap-3">
              <img
                src={item.profilePicture || item.image || userVector}
                alt={item.firstName || item.firstname}
                className="w-8 h-8 rounded-full"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">
                  {item.firstName || item.firstname} {item.lastName || item.lastname}
                </p>
                <p className="text-xs text-gray-500">
                  {item.role} • {item.isOnline ? 'Online' : 'Offline'}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                <FiUser size={16} />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <FiSearch className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={searchInputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
        />
        {query && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <FiX className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Search Results */}
      {isOpen && (query.length >= 2) && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-96 overflow-hidden"
        >
          {/* Search Tabs */}
          <div className="border-b border-gray-200 px-3 py-2">
            <div className="flex space-x-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary text-white'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon size={12} className="inline mr-1" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Results Content */}
          <div className="max-h-80 overflow-y-auto">
            {searchLoading ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                <p className="text-sm text-gray-500 mt-2">Searching...</p>
              </div>
            ) : searchError ? (
              <div className="p-4 text-center text-red-600">
                <p className="text-sm">Search failed: {searchError.message}</p>
              </div>
            ) : searchResults && Object.keys(searchResults).length > 0 ? (
              <div>
                {Object.entries(searchResults).map(([type, items]) => {
                  if (!items || items.length === 0) return null;
                  
                  return (
                    <div key={type}>
                      {activeTab === 'all' && (
                        <div className="px-3 py-2 bg-gray-50 border-b border-gray-100">
                          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                            {type}
                          </h4>
                        </div>
                      )}
                      {items.map(item => renderResultItem(item, type))}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                <FiSearch size={24} className="mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No results found for "{query}"</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatSearch;
