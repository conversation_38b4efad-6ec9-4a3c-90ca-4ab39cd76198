import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import userImage from "../../../../../assets/images/tutor2.png";
import { Button } from "@/components/button/button";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";
import { useGetUpdateStudentProfileMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { toast } from "react-toastify";
import usePost from "@/hooks/usePost";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { CustomSelect } from "@/components/select/select";

const SettingsProfile = () => {
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const { handlePost, isLoading: isUpdating } = usePost(
		useGetUpdateStudentProfileMutation
	);

	const {
		register,
		handleSubmit,
		reset,
		control,
		formState: { errors },
	} = useForm();

	const timeZones = [
		"Pacific/Midway",
		"America/Adak",
		"America/Anchorage",
		"America/Los_Angeles",
		"America/Denver",
		"America/Chicago",
		"America/New_York",
		"America/Sao_Paulo",
		"Atlantic/Reykjavik",
		"Europe/London",
		"Europe/Berlin",
		"Europe/Moscow",
		"Africa/Lagos",
		"Africa/Cairo",
		"Asia/Dubai",
		"Asia/Kolkata",
		"Asia/Jakarta",
		"Asia/Shanghai",
		"Asia/Tokyo",
		"Asia/Seoul",
		"Australia/Sydney",
		"Pacific/Auckland",
	];

	// Initialize form with user data from Redux store
	useEffect(() => {
		if (user) {
			reset({
				firstname: user?.firstname || "",
				lastname: user?.lastname || "",
				email: user?.email || "",
				phone: user?.phone || "",
				timeZone: user?.timeZone || "",
			});
		}
	}, [user, reset]);

	const onSubmit = async (formData) => {
		try {
			await handlePost({
				userId: user?.id,
				...formData,
			});
		} catch (error) {
			toast.error("Failed to update profile");
		}
	};

	const timeZoneOptions = timeZones.map((tz) => ({
		label: tz,
		value: tz,
	}));

	const currentTimezone = Array.isArray(user?.location?.address)
		? [{ label: user.location.address[0], value: user.location.address[0] }]
		: [];

	console.log("currentTimezone", currentTimezone);
	console.log("timeZoneOptions");

	return (
		<div className="md:max-w-[528px] w-auto">
			<form
				onSubmit={handleSubmit(onSubmit)}
				className="flex text-[18px] flex-col w-auto"
			>
				{/* Profile Image Upload Section (unchanged) */}
				<div className="flex gap-4">
					<div className="border rounded-md w-[145px] h-[145px] overflow-hidden">
						<img
							src={userImage}
							alt="User"
							className="object-cover w-full h-full"
						/>
					</div>
					<div className="flex flex-col items-center">
						<div className="flex flex-col">
							<label className="px-6 py-2 border rounded-md cursor-pointer">
								Upload Photo
								<input type="file" hidden aria-label="upload-photo" />
							</label>
						</div>
						<p className="text-[#4B5563] text-sm">
							Maximum size – 2MB <br />
							JPG or PNG format
						</p>
					</div>
				</div>

				<div className="text-[#1A1A40] mt-6 space-y-4">
					{/* First Name */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							First Name
						</label>
						<input
							type="text"
							{...register("firstname", { required: "First name is required" })}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.firstname && (
							<p className="text-red-500 text-sm mt-1">
								{errors.firstname.message}
							</p>
						)}
					</div>

					{/* Last Name */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Last Name
						</label>
						<input
							type="text"
							{...register("lastname", { required: "Last name is required" })}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.lastname && (
							<p className="text-red-500 text-sm mt-1">
								{errors.lastname.message}
							</p>
						)}
					</div>

					{/* Email */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Email
						</label>
						<input
							type="email"
							{...register("email", {
								required: "Email is required",
								pattern: {
									value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
									message: "Invalid email address",
								},
							})}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.email && (
							<p className="text-red-500 text-sm mt-1">
								{errors.email.message}
							</p>
						)}
					</div>

					{/* Phone Number - Fixed */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Phone Number
						</label>
						<PhoneInputWithCountry
							control={control}
							name="phone"
							rules={{
								required: "Phone number is required",
								validate: (value) => {
									const phoneRegex = /^\+?[1-9]\d{1,14}$/; // E.164 format
									return phoneRegex.test(value) || "Invalid phone number";
								},
							}}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.phone && (
							<p className="text-red-500 text-sm mt-1">
								{errors.phone.message}
							</p>
						)}
					</div>

					{/* Time Zone - Fixed */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Time Zone
						</label>
						<CustomSelect
							name="timeZone"
							control={control}
							options={timeZoneOptions}
							rules={{ required: "Time zone is required" }}
							placeholder={currentTimezone[0]?.label || "Select Time Zone"}
							className="w-full"
						/>

						{errors.timeZone && (
							<p className="text-red-500 text-sm mt-1">
								{errors.timeZone.message}
							</p>
						)}
					</div>
				</div>

				<div className="mt-6">
					<Button
						className="w-full h-[50px] mb-3"
						type="submit"
						disabled={isUpdating}
					>
						{isUpdating ? <Loader size={24} /> : "Save Changes"}
					</Button>
				</div>
			</form>
		</div>
	);
};

export default SettingsProfile;
