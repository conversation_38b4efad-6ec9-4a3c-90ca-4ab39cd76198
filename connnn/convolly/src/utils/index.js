import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const capitalizeWords = (str) => {
  const words = str?.split(" ");

  const capitalizedWords = words?.map((word) => {
    if (word.length === 0) {
      return "";
    }
    return word[0].toUpperCase() + word.slice(1);
  });

  return capitalizedWords?.join(" ");
};

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const removeDuplicate = (arr = []) => {
  const seen = new Set();
  return arr.filter((item) => {
    const key = item.tempId || item.id;

    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

export function debounce(fn, delay) {
  let timer = null;

  const debounced = (...args) => {
    if (timer) clearTimeout(timer);

    timer = setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };

  // Optional cancel method
  debounced.cancel = () => {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  return debounced;
}
// Utility function to convert UTC to local time
export const formatDisplayTime = (dateString) => {
  const date = new Date(dateString);
  const localTime = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  return localTime.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

export const timezones = [
  { value: "Africa/Lagos", label: "Lagos (UTC+1)" },
  { value: "America/New_York", label: "New York (UTC-5/-4)" },
  { value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
  { value: "Europe/London", label: "London (UTC+0/+1)" },
  { value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
  { value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
  { value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
  { value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
  { value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
  { value: "Africa/Cairo", label: "Cairo (UTC+2)" },
  { value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
  { value: "America/Denver", label: "Denver (UTC-7/-6)" },
  { value: "America/Sao_Paulo", label: "São Paulo (UTC-3)" },
  { value: "Asia/Dubai", label: "Dubai (UTC+4)" },
  { value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
  { value: "Asia/Singapore", label: "Singapore (UTC+8)" },
  { value: "Europe/Moscow", label: "Moscow (UTC+3)" },
  { value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
  { value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
  { value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
];
